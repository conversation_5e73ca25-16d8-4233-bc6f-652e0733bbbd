<script lang="ts" setup>
defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const props = defineProps({
    // 表单基础设置
  formBaseInfo: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['change'])
function onClick(type) {
  emit('change', type)
}

const jobBtns = computed(() => { 
  return props.formBaseInfo?.jobBtns || []
})
</script>

<template>
  <view class="flow-submit-footer">
    <wd-button type="info" plain @click="onClick('backPre')" v-if="jobBtns.includes('18')">
      退回
    </wd-button>
    <wd-button type="error" @click="onClick('reject')" v-if="jobBtns.includes('10')">
      拒绝
    </wd-button>
    <wd-button type="primary" @click="onClick('agree')" v-if="jobBtns.includes('0')">
      同意
    </wd-button>
  </view>
</template>

<style lang="scss" scoped>
.flow-submit-footer {
  display: flex;
  gap: 12rpx;
  width: 100%;
  background-color: #fff;
  padding: 20rpx 20rpx calc(20rpx + env(safe-area-inset-bottom));
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 66;
  :deep(.wd-button) {
    height: 85rpx;
    width: calc(33% - 20rpx);
  }
}
</style>
